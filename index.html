<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        @import url("https://fonts.googleapis.com/css?family=Lobster& family= Poppins:wght@100;200;300;400;500;680;700; 800&display=swap");
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Poppins", sans-serif;
        }
    </style >
</head>
<body>
    <form action="#" autocomplete="off">
        <h1>Form Events in JavaScript</h1>
        <div class="form-group">
            <label for="username">User Name</label>
            <input type="text" id="username" />
        </div>
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" />
        </div> 
        <div class="form-group">
            <label for="email">Course Name</label>
            <select id="course">
<option value="">Select Course</option>
<option value="C">C</option>
<option value="C++">C++</option>
<option value="Java">Java</option>
</select>
        </div> 
    </form> 
    

    <script src="helo.js"></script>
</body>
</html>